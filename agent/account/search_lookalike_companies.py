import asyncio
import json
import os
from pathlib import Path
from typing import Annotated, List, Optional

import yaml
from diskcache import <PERSON><PERSON>
from langchain_core.output_parsers import <PERSON>ydanticOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import RunnableConfig, RunnableSerializable
from langchain_core.tools import tool
from langgraph.prebuilt import InjectedState, create_react_agent
from langgraph.prebuilt.chat_agent_executor import AgentState
from loguru import logger

from agent.account.models import CompanySimilarityAnalysis, LookalikeCompaniesResult
from agent.tools.apollo import apollo
from agent.tools.tavily import tavily_search
from utils import init_model
from utils.extractors import extract_structure_response
from utils.file_handler import load_file


class SearchCompaniesState(AgentState):
    excludes: list[str]
    user_query: str
    reference_company: str


@tool(parse_docstring=True)
async def search_organizations(
    q_organization_name: str | None = None,
    q_organization_keyword_tags: list[str] | None = None,
    organization_locations: list[str] | None = None,
    organization_num_employees_ranges: list[str] | None = None,
    excludes: Annotated[List[str], InjectedState("excludes")] = [],
) -> str:
    """
    Use the Organization Search endpoint to find companies in the Apollo database. Several filters are available to help narrow your search.
    This method only provides company name, company website, company headquarters address, and LinkedIn company homepage URL.
    All of the params must be in English.

    Args:
        q_organization_name: Filter search results to include a specific company name.
        q_organization_keyword_tags: Filter search results based on keywords associated with companies.
        organization_locations: The location of the company headquarters.
        organization_num_employees_ranges: The number range of employees working for the company.
        excludes: The list of company names to exclude from the search, which is injected from the state.

    Returns:
        Search results in YAML format
    """  # noqa: E501

    result = []
    page = 1

    # use 50 as default limit to lower the agent's cost, and the apollo api has a limit of 100
    # at most times, only one api call is needed even after filtering some companies
    default_limit = 50

    try:
        while len(result) < default_limit:
            logger.info(
                f"Searching organizations, page: {page}, q_organization_name: {q_organization_name},"
                f"q_organization_keyword_tags: {q_organization_keyword_tags},"
                f"organization_locations: {organization_locations},"
                f"organization_num_employees_ranges: {organization_num_employees_ranges},"
                f"excludes: {excludes}"
            )
            response = await apollo.search_organization(
                q_organization_name=q_organization_name,
                q_organization_keyword_tags=q_organization_keyword_tags,
                organization_locations=organization_locations,
                organization_num_employees_ranges=organization_num_employees_ranges,
                exclude_organization_names=excludes,
                page=page,
            )

            if response.error:
                return f"Error: {response.error}"

            organizations = response.data or []
            result.extend(organizations)

            # if has more pages, continue to search, and the total pages is not 1
            if response.pagination and response.pagination.total_pages > 1:
                page += 1
            else:
                break
    except Exception as e:
        logger.error(f"搜索企业时出错: {str(e)}")
        return f"Error: {str(e)}"

    return f"```yaml\n{yaml.dump(result[:default_limit], indent=2)}\n```"


# Similarity threshold for filtering companies
SIMILARITY_THRESHOLD = 0.6

# create cache directory and Cache object
CACHE_DIR = Path(__file__).parent / ".cache/company_similarity"
CACHE_DIR.mkdir(parents=True, exist_ok=True)

# only enable cache in development environment
if os.environ.get("APP_ENV") == "developmentx":
    similarity_cache = Cache(
        directory=str(CACHE_DIR),
        size_limit=2**30,  # 1GB limit
        eviction_policy="least-recently-used",
    )
else:
    similarity_cache = None

# set the default cache expiry to 7 days (in seconds)
CACHE_EXPIRY = 7 * 24 * 60 * 60

chain: Optional[RunnableSerializable] = None


def get_similarity_chain() -> RunnableSerializable:
    global chain

    if chain is None:
        # 使用 LLM 分析相似性
        llm = init_model(
            # model="o4-mini-low",
            max_tokens=4096,
            model="gpt-5-mini-low",
            text={"verbosity": "low"},
            # include_thoughts=True,
            # thinking_budget=256,
        ).bind_tools([{"type": "web_search_preview"}])

        output_parser = PydanticOutputParser(pydantic_object=CompanySimilarityAnalysis)

        # 构建分析提示词
        prompt = ChatPromptTemplate.from_template("""
You are a business analyst expert. Analyze the similarity between target company and reference company
based on user requirements.

## Analysis Task:
1. Calculate similarity score (0-1, where 1 is perfect match) based on user requirements
2. Provide a concise one-sentence description of the similarity
3. List specific similarity reasons and key differences
4. Follow the lookalike criteria in `Target Company Information` section to determine the similarity score
5. Use search tool to get more information about the target company if needed

## Similarity Criteria:

1. Meet all essential conditions
2. If any exclusion condition is met, it is considered not similar
3. Meet all verification criteria

## Output language

1. Default working language: **English**
2. Use the language specified by user in messages as the working language when explicitly provided
3. All thinking and responses must be in the working language
4. Natural language arguments in tool calls must be in the working language
5. Avoid using pure lists and bullet points format in any language

## Output fields
1. description:
    if similarity_score >= {similarity_threshold}, list all specific similarity reasons
    if similarity_score < {similarity_threshold}, list all specific differences or exclusion reasons
2. company_name: the name of the target company
3. similarity_score: the similarity score between the target company and the reference company

{format_instructions}

Focus on aspects most relevant to the user requirements when calculating similarity.
Use precise decimal values for similarity_score (e.g., 0.85, 0.62, 0.91).

## User Requirements:
```
{user_query}
```
                                                  
## Reference Company Information:
```
{reference_company}
```

## Target Company Information:

```yaml
{target_company_info}
```
        """).partial(
            similarity_threshold=SIMILARITY_THRESHOLD,
            format_instructions=output_parser.get_format_instructions(),
        )
        chain = prompt | llm | output_parser

    return chain


@tool(parse_docstring=True)
async def analyze_company_similarity(
    id: str,
    name: str,
    location: str | None = None,
    website_url: str | None = None,
    user_query: Annotated[str, InjectedState("user_query")] = "",
    reference_company: Annotated[str, InjectedState("reference_company")] = "",
) -> str:
    """
    Analyze company similarity by comparing target company with reference company based on user requirements.

    This tool first gets complete organization information, then uses LLM to analyze similarity
    with reference company according to user requirements.

    Args:
        id: The unique Apollo ID of the organization
        name: The name of the organization to search for
        location: The location of the organization (city, state, or country)
        website_url: The website URL of the organization
        user_query: Injected state containing user_query
        reference_company: Injected state containing reference_company

    Returns:
        Company similarity analysis results in JSON format
    """
    logger.info(f"开始分析公司相似性 - ID: {id}, Name: {name}, Location: {location}, Website: {website_url}")

    try:
        # 从状态中获取用户查询和参考公司信息

        if not user_query or not reference_company:
            error_msg = "Missing user_query or reference_company in state"
            logger.error(error_msg)
            return json.dumps({"error": error_msg})

        # check cache first using id as key (only in development environment)
        if similarity_cache is not None:
            cache_key = f"similarity_{id}"
            cached_result = similarity_cache.get(cache_key)
            if cached_result is not None:
                logger.info(f"loaded company similarity analysis from cache for ID: {id}")
                return cached_result

        # 调用 get_complete_organization_info 获取公司详细信息
        target_company_info = await apollo.get_complete_organization_info(
            id=id,
            name=name,
            location=location,
            website_url=website_url,
        )
        yaml_company_info = yaml.dump(target_company_info)

        similarity_analysis: CompanySimilarityAnalysis = await get_similarity_chain().ainvoke(
            {
                "user_query": user_query,
                "reference_company": reference_company,
                "target_company_info": yaml_company_info,
            },
            config=RunnableConfig(run_name="analyze_company_similarity"),
        )

        if similarity_analysis.error:
            logger.error(f"分析失败: {similarity_analysis.error}")
            return f"error: {similarity_analysis.error}"

        # 根据相似度分数决定返回内容（代码判断是否过滤，而不是LLM）
        should_filter = similarity_analysis.similarity_score < SIMILARITY_THRESHOLD
        company_name = similarity_analysis.company_name

        if should_filter:
            logger.info(
                f"公司{company_name} 相似度 {similarity_analysis.similarity_score:.2f} < {SIMILARITY_THRESHOLD}，建议过滤"  # noqa: E501
            )
            filter_response = (
                f"Company: {company_name}\n"
                f"Similarity score: {similarity_analysis.similarity_score:.2f} < {SIMILARITY_THRESHOLD}, so it should be filtered out."  # noqa: E501
                f"\n\n{similarity_analysis.explanation or ''}"
            )
            # save to cache (only in development environment)
            if similarity_cache is not None:
                similarity_cache.set(cache_key, filter_response, expire=CACHE_EXPIRY)
                logger.debug(f"saved company similarity analysis to cache for ID: {id}")
            return filter_response
        else:
            logger.info(
                f"公司{company_name} 相似度 {similarity_analysis.similarity_score:.2f} >= {SIMILARITY_THRESHOLD}"
            )
            pass_response = (
                f"Similarity score: {similarity_analysis.similarity_score:.2f} >= {SIMILARITY_THRESHOLD}, recommended\n"
                f"{similarity_analysis.explanation or ''}"
                f"\n\n{yaml_company_info}"
            )
            # save to cache (only in development environment)
            if similarity_cache is not None:
                similarity_cache.set(cache_key, pass_response, expire=CACHE_EXPIRY)
                logger.debug(f"saved company similarity analysis to cache for ID: {id}")
            return pass_response

    except Exception as e:
        error_msg = f"Failed to analyze company similarity: {str(e)}"
        logger.error(error_msg)
        return f"Error: {error_msg}"


async def search_lookalike_companies(
    user_query: str,
    reference_company: str,
    excludes: Optional[List[str]] = [],
    limit: Optional[int] = 10,
) -> LookalikeCompaniesResult:
    """
    根据用户查询需求和模板公司详细信息，搜索lookalike公司列表，并返回筛选后的lookalike公司列表

    Args:
        user_query: 用户查询
        reference_company: 模板公司详细信息
        excludes: 排除的公司列表
        limit: 限制返回公司的数量，默认10

    Returns:
        LookalikeCompaniesResult: 筛选后的lookalike公司列表
    """
    logger.info("开始搜索lookalike公司列表...")

    llm = init_model(
        model="gemini-2.5-flash",
        max_tokens=20480,
        temperature=0,
        include_thoughts=True,
        thinking_budget=256,
        # model="gpt-4.1",
        # max_tokens=20480,
    )
    tools = [search_organizations, tavily_search, analyze_company_similarity]

    system_prompt = load_file(Path(__file__).parent / "prompts" / "search_lookalike_companies.md")

    if not system_prompt:
        raise ValueError("无法加载系统提示文件")

    prompt = ChatPromptTemplate.from_messages([("system", system_prompt), ("placeholder", "{messages}")])

    agent = create_react_agent(
        model=llm,
        tools=tools,
        prompt=prompt,
        state_schema=SearchCompaniesState,
        name="search_lookalike_companies_agent",
    )

    input_message = (
        f"## 用户查询需求：\n{user_query}\n\n## 模板公司信息：\n{reference_company}\n\n## 限制返回的公司数量：{limit}"
    )
    if excludes and len(excludes) > 0:
        input_message += f"\n\n## 需要排除的公司列表：\n{json.dumps(excludes)}"

    result = await agent.ainvoke(
        input={
            "messages": [("human", input_message)],
            "user_query": user_query,
            "reference_company": reference_company,
            "excludes": excludes,
        },
        config=RunnableConfig(recursion_limit=100),
    )

    last_message = result.get("messages")[-1]
    structured_result: LookalikeCompaniesResult = await extract_structure_response(
        last_message.text(), LookalikeCompaniesResult, model="gpt-4.1-mini"
    )

    # 校验结果
    if structured_result.error or len(structured_result.companies) == 0:
        raise Exception("no lookalike companies found")

    logger.info(f"搜索lookalike公司列表完成，获取到 {len(structured_result.companies)} 个候选公司")

    return structured_result


if __name__ == "__main__":
    from dotenv import load_dotenv

    load_dotenv()

    async def main():
        # 测试数据
        user_query = "I want to find some companies similar Telsim that use 5G mobile networks"
        ideal_customer_profile = "### 1. 客户画像梳理\n\n*   **基础信息**\n    *   **行业**：电信（移动通信、互联网服务）\n    *   **规模**：员工数约 26 人（小型企业）\n    *   **地域**：澳大利亚\n    *   **发展阶段**：成立于 2020 年，处于成长阶段\n*   **组织特征**\n    *   **业务模式**：提供预付费移动计划、NBN 家庭互联网服务、eSIM 国际计划。与主要网络（如 Telstra、nbn）合作提供连接服务。\n    *   **组织架构**：专注于客户满意度，提供灵活服务，无合约锁定。\n*   **技术环境**\n    *   **现有技术栈**：Amazon AWS, Apache, Bootstrap Framework, Google Tag Manager, WordPress.org, Zoho One 等。\n    *   **数字化程度**：提供在线购买、自助服务门户，数字化程度较高。\n    *   **IT 预算**：中小型企业，IT 预算可能相对有限，注重成本效益。\n*   **Keywords**：telecom, mobile sim, prepaid, postpaid, sim only plans, sim cards, cheap simcards, data only plan, 5g network, esim, unlimited data, mobile connectivity, international roaming, home internet, 4g lte, nbn service, mobile network, telstra network, travel esims, telecommunication solutions, digital sim.\n\n### 2. 痛点深度挖掘\n\n*   **业务痛点**\n    *   需要高性价比、灵活的移动通信和互联网解决方案。\n    *   对传统电信合约的束缚感到不满。\n    *   国际旅行时需要便捷、经济的漫游解决方案。\n    *   寻求可靠且覆盖范围广的网络服务。\n*   **技术痛点**\n    *   需要支持 5G 网络的高速连接。\n    *   希望简化 SIM 卡管理，倾向于 eSIM 解决方案。\n    *   需要易于激活和管理的电信服务。\n*   **成本痛点**\n    *   寻求更低的移动和互联网服务成本。\n    *   避免因合约锁定带来的额外费用。\n*   **痛点优先级**：高性价比和灵活性 > 5G 高速连接 > 国际漫游便利性 > 易于管理。\n\n### 3. 关键成功特征\n\n*   **触发事件**：用户寻求新的、更经济的移动或家庭互联网服务；国际旅行需求；对现有运营商服务不满。\n*   **应用场景**：个人用户、小型办公室/家庭办公室（SOHO）、中小企业寻求高速 5G LTE 连接解决方案。\n*   **决策因素**：价格竞争力、网络覆盖（Telstra 网络）、5G 支持、无合约锁定、灵活的套餐选项、eSIM 便利性、客户服务。\n\n### 4. Lookalike 筛选条件\n\n*   **必备条件**\n    *   **行业**：电信（移动通信服务提供商、互联网服务提供商）。\n    *   **规模**：员工数在 20-100 人之间（小型到中型企业）。\n    *   **地域**：澳大利亚。\n    *   **产品/服务**：提供 5G 移动网络服务。\n*   **优选条件**\n    *   提供预付费移动计划或无合约服务。\n    *   提供家庭互联网服务（如 NBN）。\n    *   提供 eSIM 服务。\n    *   与主要网络运营商合作。\n    *   注重客户满意度和灵活服务。\n*   **排除条件**\n    *   仅提供固定电话服务或传统企业级通信解决方案的公司。\n    *   大型跨国电信巨头（除非其特定部门符合小型企业特征）。\n*   **验证指标**\n    *   公司网站明确提及 5G 服务。\n    *   产品页面显示预付费或无合约移动套餐。\n    *   新闻稿或公司简介中提及与主要网络运营商的合作。\n    *   公司规模（员工人数）与 Telsim 相似。\n    *   公司所在地为澳大利亚。"  # noqa: E501
        company_info = {"name": "Telsim Australia & New Zealand", "website": "http://www.telsim.com.au"}
        excludes = ["Inhand Networks", "Yomojo", "Boost Mobile - Australia", "TeleChoice", "Belong", "amaysim"]

        result: LookalikeCompaniesResult = await search_lookalike_companies(
            user_query=user_query,
            reference_company=f"{yaml.dump(company_info)}\n\n{ideal_customer_profile}",
            excludes=excludes,
        )
        logger.info(f"search_lookalike_companies result: {result.model_dump_json(indent=2)}")

    asyncio.run(main())
