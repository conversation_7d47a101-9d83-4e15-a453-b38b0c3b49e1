import os

import httpx
from dotenv import load_dotenv
from loguru import logger


# notification: {
#     "message": "This is a test message, please ignore.",
#     "email": "<EMAIL>",
#     "title": "Sales Agent Notification",
# }
async def send_notification_to_zoho(notification: dict):
    async with httpx.AsyncClient() as client:
        response = await client.post(
            f"https://flow.zoho.com/*********/flow/webhook/incoming?zapikey={os.environ.get('ZOHO_NOTIFICATION_API_KEY')}&isdebug=false",
            json=notification,
        )
        logger.info(f"send notification response: {response.text}")
        return response.text


if __name__ == "__main__":
    import asyncio

    load_dotenv()

    notification = {
        "message": "Your company prospecting task is complete. 5 companies have been identified and are viewable under the 'Prospecting' section within the ABC details page.",  # noqa: E501
        "email": "<EMAIL>",
        "title": "AI Agent",
    }
    asyncio.run(send_notification_to_zoho(notification))
